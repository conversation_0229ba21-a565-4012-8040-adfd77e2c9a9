# Firebase Cloud Messaging (FCM) Implementation Guide

## Overview
This implementation provides a complete Firebase Cloud Messaging solution using the HTTP v1 API for sending push notifications to all users from the admin app.

## Features
- ✅ FCM HTTP v1 API implementation (not legacy)
- ✅ Service Account authentication
- ✅ Broadcast notifications to all users
- ✅ Targeted notifications to specific users
- ✅ Role-based notifications
- ✅ Token management and cleanup
- ✅ Foreground and background notification handling
- ✅ Admin dashboard for notification management
- ✅ Notification statistics and analytics
- ✅ Secure token storage in Firestore

## Architecture

### Frontend (Flutter)
- **FCMService**: Handles token generation, permission requests, and message handling
- **NotificationService**: Manages notification sending and statistics
- **NotificationManagementScreen**: Admin UI for sending notifications
- **Models**: FCMTokenModel and NotificationModel for data structure

### Backend (Firebase Functions)
- **sendNotification**: Main function for sending notifications via FCM v1 API
- **testNotification**: Function for testing individual tokens
- **cleanupInvalidTokens**: Removes invalid/expired tokens
- **scheduledTokenCleanup**: Daily cleanup of old inactive tokens

## Setup Instructions

### 1. Firebase Configuration
The service account key is already configured in:
```
admin_app/functions/firebase-service-account.json
```

### 2. Install Dependencies
```bash
cd admin_app
flutter pub get

cd functions
npm install
```

### 3. Deploy Firebase Functions
```bash
cd admin_app/functions
firebase deploy --only functions
```

### 4. Android Configuration
The Android manifest has been configured with:
- FCM permissions
- Notification channel setup
- Intent filters for notification clicks

### 5. iOS Configuration (if needed)
Add to `ios/Runner/Info.plist`:
```xml
<key>FirebaseMessagingAutoInitEnabled</key>
<true/>
```

## Usage

### Admin Dashboard
1. Navigate to "Push Notifications" in the admin sidebar
2. Fill in notification details:
   - Title (required)
   - Message body (required)
   - Image URL (optional)
   - Type: Broadcast, Targeted, or Role-based
   - Priority: Low, Normal, or High
3. Select targets if using targeted or role-based notifications
4. Click "Send Notification"

### Notification Types
1. **Broadcast**: Sends to all active users
2. **Targeted**: Sends to specific selected users
3. **Role-based**: Sends to users with specific roles (user, reseller, admin)

## API Reference

### Cloud Functions

#### sendNotification
```javascript
// Call from Flutter
final result = await FirebaseFunctions.instance
    .httpsCallable('sendNotification')
    .call({
      'title': 'Notification Title',
      'body': 'Notification Body',
      'type': 'broadcast', // or 'targeted', 'role'
      'targetUserIds': [], // for targeted notifications
      'targetRoles': [], // for role-based notifications
    });
```

#### testNotification
```javascript
// Test a specific token
final result = await FirebaseFunctions.instance
    .httpsCallable('testNotification')
    .call({
      'token': 'fcm_token_here',
      'title': 'Test Title',
      'body': 'Test Body',
    });
```

### Flutter Services

#### FCMService
```dart
// Initialize FCM
await FCMService().initialize();

// Get current token
String? token = FCMService().currentToken;

// Check if notifications are enabled
bool enabled = await FCMService().areNotificationsEnabled();
```

#### NotificationService
```dart
// Send notification
final result = await NotificationService().sendNotification(notification);

// Get notification statistics
final stats = await NotificationService().getNotificationStats();

// Get active tokens count
final count = await NotificationService().getActiveTokensCount();
```

## Database Structure

### Firestore Collections

#### fcm_tokens
```json
{
  "userId": "user_id",
  "token": "fcm_token",
  "platform": "android|ios|web",
  "deviceId": "unique_device_id",
  "createdAt": "timestamp",
  "lastUsed": "timestamp",
  "isActive": true,
  "deviceInfo": {
    "platform": "android",
    "timestamp": "iso_string"
  }
}
```

#### notifications
```json
{
  "title": "Notification Title",
  "body": "Notification Body",
  "imageUrl": "optional_image_url",
  "type": "broadcast|targeted|role",
  "priority": "low|normal|high",
  "targetUserIds": ["user1", "user2"],
  "targetRoles": ["user", "reseller"],
  "createdBy": "admin_user_id",
  "createdAt": "timestamp",
  "isSent": true,
  "totalRecipients": 100,
  "successfulSends": 95,
  "failedSends": 5
}
```

## Security Features

### Authentication
- All Cloud Functions require admin authentication
- Service account key is securely stored
- Token validation and cleanup

### Rate Limiting
- Batch processing for large notification sends
- Invalid token cleanup to prevent spam
- Daily scheduled cleanup of old tokens

### Data Validation
- Input validation on all notification parameters
- Token format validation
- User role verification

## Monitoring and Analytics

### Dashboard Statistics
- Today's notifications sent
- Weekly notification count
- Active FCM tokens
- Success/failure rates

### Logging
- All notification sends are logged
- Admin actions are tracked
- Error logging for debugging

## Troubleshooting

### Common Issues

1. **Notifications not received**
   - Check if FCM token is valid
   - Verify user has granted notification permissions
   - Check if app is in background/foreground

2. **Token not generated**
   - Ensure Firebase is properly initialized
   - Check internet connection
   - Verify Firebase configuration

3. **Function deployment fails**
   - Check service account permissions
   - Verify Firebase project configuration
   - Ensure all dependencies are installed

### Debug Commands
```bash
# Check function logs
firebase functions:log

# Test function locally
firebase emulators:start --only functions

# Validate service account
firebase auth:export users.json
```

## Best Practices

1. **Token Management**
   - Regularly clean up invalid tokens
   - Update tokens on app updates
   - Handle token refresh properly

2. **Notification Content**
   - Keep titles under 100 characters
   - Keep body under 500 characters
   - Use appropriate priority levels

3. **Performance**
   - Batch large notification sends
   - Use appropriate retry logic
   - Monitor success rates

4. **Security**
   - Validate all inputs
   - Use proper authentication
   - Log all admin actions

## Support
For issues or questions, check the Firebase documentation or contact the development team.
