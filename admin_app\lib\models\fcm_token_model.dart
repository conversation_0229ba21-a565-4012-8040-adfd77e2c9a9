import 'package:cloud_firestore/cloud_firestore.dart';

class FCMTokenModel {
  final String id;
  final String userId;
  final String token;
  final String platform; // 'android', 'ios', 'web'
  final String deviceId;
  final DateTime createdAt;
  final DateTime lastUsed;
  final bool isActive;
  final Map<String, dynamic>? deviceInfo;

  FCMTokenModel({
    required this.id,
    required this.userId,
    required this.token,
    required this.platform,
    required this.deviceId,
    required this.createdAt,
    required this.lastUsed,
    this.isActive = true,
    this.deviceInfo,
  });

  factory FCMTokenModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FCMTokenModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      token: data['token'] ?? '',
      platform: data['platform'] ?? '',
      deviceId: data['deviceId'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastUsed: (data['lastUsed'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
      deviceInfo: data['deviceInfo'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'token': token,
      'platform': platform,
      'deviceId': deviceId,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastUsed': Timestamp.fromDate(lastUsed),
      'isActive': isActive,
      'deviceInfo': deviceInfo,
    };
  }

  FCMTokenModel copyWith({
    String? id,
    String? userId,
    String? token,
    String? platform,
    String? deviceId,
    DateTime? createdAt,
    DateTime? lastUsed,
    bool? isActive,
    Map<String, dynamic>? deviceInfo,
  }) {
    return FCMTokenModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      token: token ?? this.token,
      platform: platform ?? this.platform,
      deviceId: deviceId ?? this.deviceId,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
      isActive: isActive ?? this.isActive,
      deviceInfo: deviceInfo ?? this.deviceInfo,
    );
  }

  @override
  String toString() {
    return 'FCMTokenModel(id: $id, userId: $userId, platform: $platform, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FCMTokenModel &&
        other.id == id &&
        other.userId == userId &&
        other.token == token;
  }

  @override
  int get hashCode => id.hashCode ^ userId.hashCode ^ token.hashCode;
}
