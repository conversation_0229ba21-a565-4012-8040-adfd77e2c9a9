import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_functions/cloud_functions.dart';
import '../models/notification_model.dart';
import '../models/fcm_token_model.dart';
import '../models/user_model.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  // Send notification via Cloud Function
  Future<Map<String, dynamic>> sendNotification(NotificationModel notification) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Store notification in Firestore first
      final docRef = await _firestore.collection('notifications').add(notification.toFirestore());
      
      // Call Cloud Function to send the notification
      final callable = _functions.httpsCallable('sendNotification');
      final result = await callable.call({
        'notificationId': docRef.id,
        'title': notification.title,
        'body': notification.body,
        'imageUrl': notification.imageUrl,
        'data': notification.data,
        'type': notification.type.toString().split('.').last,
        'priority': notification.priority.toString().split('.').last,
        'targetUserIds': notification.targetUserIds,
        'targetRoles': notification.targetRoles,
      });

      return {
        'success': true,
        'notificationId': docRef.id,
        'result': result.data,
      };
    } catch (e) {
      print('Error sending notification: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Get all notifications (for admin dashboard)
  Stream<List<NotificationModel>> getNotifications({int limit = 50}) {
    return _firestore
        .collection('notifications')
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => NotificationModel.fromFirestore(doc))
            .toList());
  }

  // Get notification by ID
  Future<NotificationModel?> getNotificationById(String id) async {
    try {
      final doc = await _firestore.collection('notifications').doc(id).get();
      if (doc.exists) {
        return NotificationModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting notification: $e');
      return null;
    }
  }

  // Get active FCM tokens count
  Future<int> getActiveTokensCount() async {
    try {
      final snapshot = await _firestore
          .collection('fcm_tokens')
          .where('isActive', isEqualTo: true)
          .count()
          .get();
      return snapshot.count ?? 0;
    } catch (e) {
      print('Error getting active tokens count: $e');
      return 0;
    }
  }

  // Get FCM tokens for specific users
  Future<List<FCMTokenModel>> getTokensForUsers(List<String> userIds) async {
    try {
      if (userIds.isEmpty) return [];

      final snapshot = await _firestore
          .collection('fcm_tokens')
          .where('userId', whereIn: userIds)
          .where('isActive', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => FCMTokenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting tokens for users: $e');
      return [];
    }
  }

  // Get all active FCM tokens
  Future<List<FCMTokenModel>> getAllActiveTokens() async {
    try {
      final snapshot = await _firestore
          .collection('fcm_tokens')
          .where('isActive', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => FCMTokenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting all active tokens: $e');
      return [];
    }
  }

  // Get users by role for role-based notifications
  Future<List<UserModel>> getUsersByRole(List<String> roles) async {
    try {
      if (roles.isEmpty) return [];

      final snapshot = await _firestore
          .collection('users')
          .where('role', whereIn: roles)
          .get();

      return snapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting users by role: $e');
      return [];
    }
  }

  // Clean up invalid tokens
  Future<void> cleanupInvalidTokens() async {
    try {
      final callable = _functions.httpsCallable('cleanupInvalidTokens');
      await callable.call();
    } catch (e) {
      print('Error cleaning up invalid tokens: $e');
    }
  }

  // Test notification to specific token
  Future<Map<String, dynamic>> testNotification(String token) async {
    try {
      final callable = _functions.httpsCallable('testNotification');
      final result = await callable.call({
        'token': token,
        'title': 'Test Notification',
        'body': 'This is a test notification from the admin panel.',
      });

      return {
        'success': true,
        'result': result.data,
      };
    } catch (e) {
      print('Error sending test notification: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Get notification statistics
  Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final startOfWeek = startOfDay.subtract(Duration(days: now.weekday - 1));
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Get today's notifications
      final todaySnapshot = await _firestore
          .collection('notifications')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .count()
          .get();

      // Get this week's notifications
      final weekSnapshot = await _firestore
          .collection('notifications')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfWeek))
          .count()
          .get();

      // Get this month's notifications
      final monthSnapshot = await _firestore
          .collection('notifications')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .count()
          .get();

      // Get total notifications
      final totalSnapshot = await _firestore
          .collection('notifications')
          .count()
          .get();

      // Get active tokens count
      final activeTokens = await getActiveTokensCount();

      return {
        'today': todaySnapshot.count ?? 0,
        'week': weekSnapshot.count ?? 0,
        'month': monthSnapshot.count ?? 0,
        'total': totalSnapshot.count ?? 0,
        'activeTokens': activeTokens,
      };
    } catch (e) {
      print('Error getting notification stats: $e');
      return {
        'today': 0,
        'week': 0,
        'month': 0,
        'total': 0,
        'activeTokens': 0,
      };
    }
  }

  // Delete notification
  Future<bool> deleteNotification(String id) async {
    try {
      await _firestore.collection('notifications').doc(id).delete();
      return true;
    } catch (e) {
      print('Error deleting notification: $e');
      return false;
    }
  }

  // Update notification status
  Future<bool> updateNotificationStatus(String id, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('notifications').doc(id).update(updates);
      return true;
    } catch (e) {
      print('Error updating notification status: $e');
      return false;
    }
  }
}
