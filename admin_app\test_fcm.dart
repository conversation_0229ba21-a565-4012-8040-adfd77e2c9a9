import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/services/fcm_service.dart';
import 'lib/firebase_options.dart';

/// Simple test app to verify FCM setup
/// Run with: flutter run test_fcm.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // Initialize FCM
  await FCMService().initialize();
  
  runApp(const FCMTestApp());
}

class FCMTestApp extends StatelessWidget {
  const FCMTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FCM Test',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const FCMTestScreen(),
    );
  }
}

class FCMTestScreen extends StatefulWidget {
  const FCMTestScreen({super.key});

  @override
  State<FCMTestScreen> createState() => _FCMTestScreenState();
}

class _FCMTestScreenState extends State<FCMTestScreen> {
  final FCMService _fcmService = FCMService();
  String _status = 'Initializing...';
  String? _token;
  bool _notificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkFCMStatus();
  }

  Future<void> _checkFCMStatus() async {
    try {
      // Get current token
      final token = _fcmService.currentToken;
      
      // Check if notifications are enabled
      final enabled = await _fcmService.areNotificationsEnabled();
      
      setState(() {
        _token = token;
        _notificationsEnabled = enabled;
        _status = enabled ? 'FCM Ready' : 'Notifications Disabled';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FCM Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _notificationsEnabled ? Icons.check_circle : Icons.error,
                          color: _notificationsEnabled ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(_status),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FCM Token',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    if (_token != null) ...[
                      Text(
                        'Token (first 50 chars):',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _token!.length > 50 
                              ? '${_token!.substring(0, 50)}...'
                              : _token!,
                          style: const TextStyle(fontFamily: 'monospace'),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () {
                          // Copy token to clipboard
                          // You can implement clipboard functionality here
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Token: $_token'),
                              duration: const Duration(seconds: 5),
                            ),
                          );
                        },
                        child: const Text('Show Full Token'),
                      ),
                    ] else ...[
                      const Text('No token available'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Instructions',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. Make sure FCM status shows "FCM Ready"\n'
                      '2. Copy the FCM token\n'
                      '3. Use the admin panel to send a test notification\n'
                      '4. Or use the testNotification Cloud Function\n'
                      '5. Check if notification is received',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _checkFCMStatus,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Refresh Status'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
