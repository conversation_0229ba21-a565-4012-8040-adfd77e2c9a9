import 'dart:async';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/fcm_token_model.dart';

// Top-level function for background message handling
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling a background message: ${message.messageId}');
  print('Message data: ${message.data}');
  print('Message notification: ${message.notification?.title}');
}

class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? _currentToken;
  StreamSubscription<String>? _tokenRefreshSubscription;
  StreamSubscription<RemoteMessage>? _foregroundMessageSubscription;

  // Initialize FCM service
  Future<void> initialize() async {
    try {
      // Set the background messaging handler early on
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      // Request permission for notifications
      await _requestPermission();

      // Get initial token
      await _getAndStoreToken();

      // Listen for token refresh
      _listenForTokenRefresh();

      // Handle foreground messages
      _handleForegroundMessages();

      // Handle notification taps
      _handleNotificationTaps();

      print('FCM Service initialized successfully');
    } catch (e) {
      print('Error initializing FCM Service: $e');
    }
  }

  // Request notification permissions
  Future<bool> _requestPermission() async {
    try {
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      print('User granted permission: ${settings.authorizationStatus}');
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (e) {
      print('Error requesting permission: $e');
      return false;
    }
  }

  // Get FCM token and store it
  Future<String?> _getAndStoreToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        _currentToken = token;
        await _storeTokenInFirestore(token);
        await _storeTokenLocally(token);
        print('FCM Token obtained: ${token.substring(0, 20)}...');
      }
      return token;
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  // Store token in Firestore
  Future<void> _storeTokenInFirestore(String token) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final deviceId = await _getDeviceId();
      final platform = _getPlatform();

      // Check if token already exists for this device
      final existingTokenQuery = await _firestore
          .collection('fcm_tokens')
          .where('userId', isEqualTo: user.uid)
          .where('deviceId', isEqualTo: deviceId)
          .limit(1)
          .get();

      final tokenData = FCMTokenModel(
        id: '',
        userId: user.uid,
        token: token,
        platform: platform,
        deviceId: deviceId,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
        isActive: true,
        deviceInfo: await _getDeviceInfo(),
      );

      if (existingTokenQuery.docs.isNotEmpty) {
        // Update existing token
        final docId = existingTokenQuery.docs.first.id;
        await _firestore
            .collection('fcm_tokens')
            .doc(docId)
            .update(tokenData.copyWith(id: docId).toFirestore());
      } else {
        // Create new token document
        await _firestore.collection('fcm_tokens').add(tokenData.toFirestore());
      }

      // Deactivate old tokens for this user on different devices
      await _deactivateOldTokens(user.uid, deviceId);
    } catch (e) {
      print('Error storing token in Firestore: $e');
    }
  }

  // Store token locally
  Future<void> _storeTokenLocally(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
    } catch (e) {
      print('Error storing token locally: $e');
    }
  }

  // Listen for token refresh
  void _listenForTokenRefresh() {
    _tokenRefreshSubscription = _firebaseMessaging.onTokenRefresh.listen((token) {
      print('FCM Token refreshed');
      _currentToken = token;
      _storeTokenInFirestore(token);
      _storeTokenLocally(token);
    });
  }

  // Handle foreground messages
  void _handleForegroundMessages() {
    _foregroundMessageSubscription = FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');

      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
        _showForegroundNotification(message);
      }
    });
  }

  // Handle notification taps
  void _handleNotificationTaps() {
    // Handle notification tap when app is terminated
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        print('App opened from terminated state via notification');
        _handleNotificationTap(message);
      }
    });

    // Handle notification tap when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('App opened from background via notification');
      _handleNotificationTap(message);
    });
  }

  // Show foreground notification
  void _showForegroundNotification(RemoteMessage message) {
    // You can implement a custom in-app notification here
    // For now, we'll just print the message
    print('Foreground notification: ${message.notification?.title}');
  }

  // Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    print('Notification tapped: ${message.data}');
    // Implement navigation logic based on message data
    // Example: Navigate to specific screen based on message.data['screen']
  }

  // Get device ID
  Future<String> _getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString('device_id');

      if (deviceId == null) {
        deviceId = 'device_${DateTime.now().millisecondsSinceEpoch}_${_getPlatform()}';
        await prefs.setString('device_id', deviceId);
      }

      return deviceId;
    } catch (e) {
      return 'device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  // Get platform
  String _getPlatform() {
    if (kIsWeb) return 'web';
    if (Platform.isAndroid) return 'android';
    if (Platform.isIOS) return 'ios';
    return 'unknown';
  }

  // Get device info
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': _getPlatform(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  // Deactivate old tokens
  Future<void> _deactivateOldTokens(String userId, String currentDeviceId) async {
    try {
      final oldTokensQuery = await _firestore
          .collection('fcm_tokens')
          .where('userId', isEqualTo: userId)
          .where('deviceId', isNotEqualTo: currentDeviceId)
          .get();

      final batch = _firestore.batch();
      for (final doc in oldTokensQuery.docs) {
        batch.update(doc.reference, {'isActive': false});
      }
      await batch.commit();
    } catch (e) {
      print('Error deactivating old tokens: $e');
    }
  }

  // Get current token
  String? get currentToken => _currentToken;

  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final settings = await _firebaseMessaging.getNotificationSettings();
    return settings.authorizationStatus == AuthorizationStatus.authorized;
  }

  // Dispose resources
  void dispose() {
    _tokenRefreshSubscription?.cancel();
    _foregroundMessageSubscription?.cancel();
  }
}
