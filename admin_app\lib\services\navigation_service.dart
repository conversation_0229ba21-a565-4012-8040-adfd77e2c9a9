import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/navigation_models.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  // Navigation Groups
  List<NavigationGroup> getNavigationItems() {
    return [
      NavigationGroup(
        title: 'Dashboard',
        items: [
          NavigationItem(
            icon: Icons.dashboard_outlined,
            activeIcon: Icons.dashboard,
            label: 'Overview',
            index: 0,
            route: '/dashboard',
          ),
        ],
      ),
      NavigationGroup(
        title: 'User Management',
        items: [
          NavigationItem(
            icon: Icons.people_outline,
            activeIcon: Icons.people,
            label: 'Users',
            index: 1,
            route: '/users',
          ),
          NavigationItem(
            icon: Icons.verified_user_outlined,
            activeIcon: Icons.verified_user,
            label: 'Resellers',
            index: 2,
            route: '/resellers',
          ),
          NavigationItem(
            icon: Icons.admin_panel_settings_outlined,
            activeIcon: Icons.admin_panel_settings,
            label: 'Admins',
            index: 3,
            route: '/admins',
          ),
          NavigationItem(
            icon: Icons.person_add_outlined,
            activeIcon: Icons.person_add,
            label: 'Pending Registrations',
            index: 4,
            route: '/pending-registrations',
          ),
          NavigationItem(
            icon: Icons.store_outlined,
            activeIcon: Icons.store,
            label: 'Reseller Applications',
            index: 5,
            route: '/reseller-applications',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Content Management',
        items: [
          NavigationItem(
            icon: Icons.post_add_outlined,
            activeIcon: Icons.post_add,
            label: 'Posts',
            index: 6,
            route: '/posts',
          ),
          NavigationItem(
            icon: Icons.comment_outlined,
            activeIcon: Icons.comment,
            label: 'Comments',
            index: 7,
            route: '/comments',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Product Management',
        items: [
          NavigationItem(
            icon: Icons.shopping_bag_outlined,
            activeIcon: Icons.shopping_bag,
            label: 'Products',
            index: 8,
            route: '/products',
          ),
          NavigationItem(
            icon: Icons.pending_actions_outlined,
            activeIcon: Icons.pending_actions,
            label: 'Pending Products',
            index: 9,
            route: '/pending-products',
          ),
          NavigationItem(
            icon: Icons.category_outlined,
            activeIcon: Icons.category,
            label: 'Categories',
            index: 10,
            route: '/categories',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Support',
        items: [
          NavigationItem(
            icon: Icons.support_agent_outlined,
            activeIcon: Icons.support_agent,
            label: 'Support Management',
            index: 10,
            route: '/support',
          ),
          NavigationItem(
            icon: Icons.report_outlined,
            activeIcon: Icons.report,
            label: 'Reports',
            index: 13,
            route: '/reports',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Marketing',
        items: [
          NavigationItem(
            icon: Icons.slideshow_outlined,
            activeIcon: Icons.slideshow,
            label: 'Slider Management',
            index: 11,
            route: '/sliders',
          ),
          NavigationItem(
            icon: Icons.people_outline,
            activeIcon: Icons.people,
            label: 'Referral Management',
            index: 12,
            route: '/referrals',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Communication',
        items: [
          NavigationItem(
            icon: Icons.notifications_outlined,
            activeIcon: Icons.notifications,
            label: 'Push Notifications',
            index: 13,
            route: '/notifications',
          ),
        ],
      ),
      NavigationGroup(
        title: 'System',
        items: [

          NavigationItem(
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: 'Settings',
            index: 14,
            route: '/settings',
          ),
        ],
      ),
    ];
  }

  // Get all navigation items flattened
  List<NavigationItem> getAllNavigationItems() {
    return getNavigationItems().expand((group) => group.items).toList();
  }

  // Get navigation item by route
  NavigationItem? getNavigationItemByRoute(String route) {
    try {
      return getAllNavigationItems().firstWhere(
        (item) => item.route == route,
      );
    } catch (e) {
      // Return null if not found instead of a fallback item
      return null;
    }
  }
}
