import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../enums/user_role.dart';
import '../services/user_management_service.dart';
import '../widgets/user_management/user_statistics_widget.dart';
import '../widgets/common/swipe_refresh_mixin.dart';
import 'user_detail_screen.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> with SwipeRefreshMixin {
  final TextEditingController _searchController = TextEditingController();
  List<UserModel> _users = [];
  List<UserModel> _filteredUsers = [];
  bool _isLoading = true;
  UserRole? _selectedRole;

  String? _selectedActiveStatus; // New filter for active/inactive status
  bool _showStatistics = false; // Default to collapsed

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      bool? isActiveFilter;
      if (_selectedActiveStatus == 'active') {
        isActiveFilter = true;
      } else if (_selectedActiveStatus == 'inactive') {
        isActiveFilter = false;
      }

      final users = await UserManagementService.getUsers(
        limit: 100,
        roleFilter: _selectedRole,
        isActiveFilter: isActiveFilter,
      );

      // Filter out pending users from main user list (they should only appear in pending approval page)
      final approvedUsers = users.where((user) =>
        user.registrationStatus.isApproved ||
        user.registrationStatus.isRejected
      ).toList();

      setState(() {
        _users = approvedUsers;
        _filteredUsers = approvedUsers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load users: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  @override
  Future<void> onRefresh() async {
    await _loadUsers();
  }

  void _filterUsers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredUsers = _users.where((user) {
        final matchesSearch = query.isEmpty ||
            user.username.toLowerCase().contains(query) ||
            user.displayName.toLowerCase().contains(query) ||
            user.email.toLowerCase().contains(query);
        
        bool roleMatch = true;
        if (_selectedRole != null) {
          roleMatch = user.role == _selectedRole;
        }
        
        bool statusMatch = true;

        bool activeMatch = true;
        if (_selectedActiveStatus != null) {
          if (_selectedActiveStatus == 'active') {
            activeMatch = user.isActive;
          } else if (_selectedActiveStatus == 'inactive') {
            activeMatch = !user.isActive;
          }
        }

        return matchesSearch && roleMatch && statusMatch && activeMatch;
      }).toList();
    });
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search users...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide.none,
          ),
          filled: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              _filterUsers();
            },
          ),
        ),
        onChanged: (_) => _filterUsers(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.people, size: 24, color: Colors.black),
            const SizedBox(width: 8),
            const Text('User Management', style: TextStyle(color: Colors.black)),
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_filteredUsers.length} users',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pushReplacementNamed('/dashboard'),
          tooltip: 'Back to Overview',
        ),
        actions: [
          IconButton(
            icon: Icon(
              _showStatistics ? Icons.bar_chart : Icons.bar_chart_outlined,
              color: Colors.black,
            ),
            onPressed: () {
              setState(() {
                _showStatistics = !_showStatistics;
              });
            },
            tooltip: _showStatistics ? 'Hide Statistics' : 'Show Statistics',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black),
            onPressed: _loadUsers,
            tooltip: 'Refresh Users',
          ),
        ],
      ),
      body: buildWithRefresh(
        Column(
          children: [
            // Statistics (toggleable)
            if (_showStatistics)
              const Padding(
                padding: EdgeInsets.all(AppConstants.paddingMedium),
                child: UserStatisticsWidget(),
              ),

            // Search and Filters
            _buildSearchAndFilters(),

            // Users List
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredUsers.isEmpty
                      ? _buildEmptyState()
                      : _buildUsersList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            style: const TextStyle(color: Colors.black),
            decoration: const InputDecoration(
              hintText: 'Search users by name, username, or email...',
              hintStyle: TextStyle(color: Colors.grey),
              prefixIcon: Icon(Icons.search, color: Colors.black),
              labelStyle: TextStyle(color: Colors.black),
            ),
            onChanged: (_) => _filterUsers(),
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Filters
          Row(
            children: [
              // Role Filter
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<UserRole?>(
                  value: _selectedRole,
                  decoration: InputDecoration(
                    labelText: 'Role',
                    labelStyle: const TextStyle(color: Colors.black),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  style: const TextStyle(color: Colors.black),
                  items: [
                    const DropdownMenuItem<UserRole?>(
                      value: null,
                      child: Text('All Roles'),
                    ),
                    ...UserRole.values.map((role) => DropdownMenuItem(
                      value: role,
                      child: Text(role.displayName),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedRole = value;
                    });
                    _loadUsers();
                  },
                ),
              ),



              const SizedBox(width: AppConstants.paddingMedium),

              // Active Status Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedActiveStatus,
                  decoration: const InputDecoration(
                    labelText: 'Active Status',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: null,
                      child: Text('All Status'),
                    ),
                    DropdownMenuItem(
                      value: 'active',
                      child: Text('Active'),
                    ),
                    DropdownMenuItem(
                      value: 'inactive',
                      child: Text('Inactive'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedActiveStatus = value;
                    });
                    _filterUsers();
                  },
                ),
              ),


            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: AppConstants.textHintColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No users found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textHintColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge), // Increased padding
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 25,
              backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
              backgroundImage: user.profileImageUrl != null
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child: user.profileImageUrl == null
                  ? Text(
                      user.displayName.isNotEmpty
                          ? user.displayName[0].toUpperCase()
                          : user.email[0].toUpperCase(),
                      style: const TextStyle(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            
            const SizedBox(width: AppConstants.paddingMedium),
            
            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        user.displayNameOrUsername,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingSmall),
                      _buildRoleBadge(user.role),
                      if (user.isVerified) ...[
                        const SizedBox(width: AppConstants.paddingSmall),
                        const Icon(
                          Icons.verified,
                          size: 16,
                          color: AppConstants.successColor,
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    user.email,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Text(
                        'Joined: ${user.formattedJoinDate}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.textHintColor,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      // Active/Inactive Status Badge
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingSmall,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: user.isActive
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              user.isActive ? Icons.check_circle : Icons.block,
                              size: 12,
                              color: user.isActive ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              user.isActive ? 'Active' : 'Inactive',
                              style: TextStyle(
                                fontSize: AppConstants.fontSizeSmall,
                                color: user.isActive ? Colors.green : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Actions
            PopupMenuButton<String>(
              onSelected: (action) => _handleUserAction(action, user),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: ListTile(
                    leading: Icon(Icons.visibility),
                    title: Text('View Details'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit_profile',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit Profile'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'change_password',
                  child: ListTile(
                    leading: Icon(Icons.lock_reset),
                    title: Text('Password Change'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'change_role',
                  child: ListTile(
                    leading: Icon(Icons.admin_panel_settings),
                    title: Text('Role Change'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                PopupMenuItem(
                  value: user.isActive ? 'deactivate' : 'activate',
                  child: ListTile(
                    leading: Icon(user.isActive ? Icons.block : Icons.check_circle),
                    title: Text(user.isActive ? 'Deactivate User' : 'Activate User'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('Delete User', style: TextStyle(color: Colors.red)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleBadge(UserRole role) {
    Color color;
    switch (role) {
      case UserRole.admin:
        color = AppConstants.errorColor;
        break;
      case UserRole.reseller:
        color = AppConstants.warningColor;
        break;
      case UserRole.user:
      default:
        color = AppConstants.primaryColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Text(
        role.displayName,
        style: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _handleUserAction(String action, UserModel user) {
    switch (action) {
      case 'view':
        _showUserDetails(user);
        break;
      case 'edit_profile':
        _showEditProfileDialog(user);
        break;
      case 'change_password':
        _showChangePasswordDialog(user);
        break;
      case 'activate':
        _reactivateUser(user);
        break;
      case 'deactivate':
        _showDeactivateUserDialog(user);
        break;
      case 'change_role':
        _showChangeRoleDialog(user);
        break;
      case 'delete':
        _showDeleteUserDialog(user);
        break;
    }
  }

  void _showUserDetails(UserModel user) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(user: user),
      ),
    );
  }

  void _showEditProfileDialog(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _EditProfileDialog(
        user: user,
        onProfileUpdated: () {
          _loadUsers();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile updated successfully'),
                backgroundColor: AppConstants.successColor,
              ),
            );
          }
        },
      ),
    );
  }

  void _showChangePasswordDialog(UserModel user) {
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Change Password'),
          content: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Change password for ${user.displayNameOrUsername}',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: newPasswordController,
                  obscureText: obscureNewPassword,
                  decoration: InputDecoration(
                    labelText: 'New Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureNewPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscureNewPassword = !obscureNewPassword;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: confirmPasswordController,
                  obscureText: obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'Confirm Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscureConfirmPassword = !obscureConfirmPassword;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newPassword = newPasswordController.text.trim();
                final confirmPassword = confirmPasswordController.text.trim();

                if (newPassword.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a new password'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                  return;
                }

                if (newPassword.length < 6) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Password must be at least 6 characters'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                  return;
                }

                if (newPassword != confirmPassword) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Passwords do not match'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();
                try {
                  await UserManagementService.changeUserPassword(user.id, newPassword);
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Password changed successfully for ${user.displayNameOrUsername}'),
                        backgroundColor: AppConstants.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to change password: $e'),
                        backgroundColor: AppConstants.errorColor,
                      ),
                    );
                  }
                }
              },
              child: const Text('Change Password'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteUserDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning,
              color: AppConstants.errorColor,
              size: 48,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Are you sure you want to delete ${user.displayNameOrUsername}?',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            const Text(
              'This action cannot be undone. All user data will be permanently removed.',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await UserManagementService.deleteUserPermanently(user.id);
                _loadUsers();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayNameOrUsername} deleted successfully'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete user: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _toggleUserStatus(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${user.isActive ? 'Deactivate' : 'Activate'} User'),
        content: Text(
          'Are you sure you want to ${user.isActive ? 'deactivate' : 'activate'} ${user.displayNameOrUsername}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await UserManagementService.toggleUserActiveStatus(
                  user.id,
                  !user.isActive,
                );
                _loadUsers();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'User ${user.isActive ? 'deactivated' : 'activated'} successfully',
                      ),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to update user: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            child: Text(user.isActive ? 'Deactivate' : 'Activate'),
          ),
        ],
      ),
    );
  }



  void _showChangeRoleDialog(UserModel user) {
    UserRole? selectedRole = user.role;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Change Role for ${user.displayNameOrUsername}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Current role: ${user.role.displayName}'),
              const SizedBox(height: AppConstants.paddingMedium),
              DropdownButtonFormField<UserRole>(
                value: selectedRole,
                decoration: const InputDecoration(
                  labelText: 'New Role',
                ),
                items: UserRole.values
                    .map((role) => DropdownMenuItem(
                      value: role,
                      child: Row(
                        children: [
                          Icon(
                            role.isAdmin ? Icons.admin_panel_settings :
                            role.isReseller ? Icons.store : Icons.person,
                            size: 16,
                            color: role.isAdmin ? Colors.red :
                                   role.isReseller ? Colors.orange : Colors.blue,
                          ),
                          const SizedBox(width: 8),
                          Text(role.displayName),
                        ],
                      ),
                    ))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    selectedRole = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: selectedRole == user.role
                  ? null
                  : () async {
                      Navigator.of(context).pop();
                      try {
                        await UserManagementService.updateUserRole(
                          user.id,
                          selectedRole!,
                        );
                        _loadUsers();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('User role updated successfully'),
                              backgroundColor: AppConstants.successColor,
                            ),
                          );
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Failed to update role: $e'),
                              backgroundColor: AppConstants.errorColor,
                            ),
                          );
                        }
                      }
                    },
              child: const Text('Update Role'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeactivateUserDialog(UserModel user) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Deactivate User'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to deactivate ${user.displayNameOrUsername}?',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Text(
                'This will prevent the user from accessing the application.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              TextField(
                controller: reasonController,
                decoration: const InputDecoration(
                  labelText: 'Reason for deactivation',
                  border: OutlineInputBorder(),
                  hintText: 'Enter reason (optional)',
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await UserManagementService.deactivateUser(
                  user.id,
                  reasonController.text.trim().isEmpty
                    ? 'Deactivated by admin'
                    : reasonController.text.trim(),
                );
                _loadUsers();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('User deactivated successfully'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to deactivate user: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  void _reactivateUser(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reactivate User'),
        content: Text(
          'Are you sure you want to reactivate ${user.displayNameOrUsername}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await UserManagementService.reactivateUser(user.id);
                _loadUsers();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('User reactivated successfully'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to reactivate user: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.successColor,
            ),
            child: const Text('Reactivate'),
          ),
        ],
      ),
    );
  }


}

class _EditProfileDialog extends StatefulWidget {
  final UserModel user;
  final VoidCallback onProfileUpdated;

  const _EditProfileDialog({
    required this.user,
    required this.onProfileUpdated,
  });

  @override
  State<_EditProfileDialog> createState() => _EditProfileDialogState();
}

class _EditProfileDialogState extends State<_EditProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _displayNameController;
  late final TextEditingController _usernameController;
  late final TextEditingController _bioController;
  late final TextEditingController _mobileController;
  late final TextEditingController _addressController;
  late final TextEditingController _countryController;
  String? _selectedGender;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    print('EditProfileDialog: Initializing edit profile dialog for user ${widget.user.id}');
    print('EditProfileDialog: User data - Display Name: ${widget.user.displayName}');
    print('EditProfileDialog: User data - Username: ${widget.user.username}');
    print('EditProfileDialog: User data - Bio: ${widget.user.bio ?? 'null'}');
    print('EditProfileDialog: User data - Mobile: ${widget.user.mobile ?? 'null'}');
    print('EditProfileDialog: User data - Address: ${widget.user.address ?? 'null'}');
    print('EditProfileDialog: User data - Country: ${widget.user.country ?? 'null'}');
    print('EditProfileDialog: User data - Gender: ${widget.user.gender ?? 'null'}');

    _displayNameController = TextEditingController(text: widget.user.displayName);
    _usernameController = TextEditingController(text: widget.user.username);
    _bioController = TextEditingController(text: widget.user.bio ?? '');
    _mobileController = TextEditingController(text: widget.user.mobile ?? '');
    _addressController = TextEditingController(text: widget.user.address ?? '');
    _countryController = TextEditingController(text: widget.user.country ?? '');
    _selectedGender = widget.user.gender;

    print('EditProfileDialog: Controllers initialized successfully');
  }

  @override
  void dispose() {
    print('EditProfileDialog: Disposing controllers and cleaning up resources');
    _displayNameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    _mobileController.dispose();
    _addressController.dispose();
    _countryController.dispose();
    super.dispose();
    print('EditProfileDialog: Dispose completed');
  }

  Future<void> _updateProfile() async {
    print('EditProfileDialog: Starting profile update validation');

    if (!_formKey.currentState!.validate()) {
      print('EditProfileDialog: Form validation failed');
      return;
    }

    print('EditProfileDialog: Form validation passed, starting update process');
    setState(() {
      _isLoading = true;
    });

    try {
      print('EditProfileDialog: Preparing update data for user ${widget.user.id}');
      print('EditProfileDialog: Display Name: ${_displayNameController.text.trim()}');
      print('EditProfileDialog: Username: ${_usernameController.text.trim()}');
      print('EditProfileDialog: Bio: ${_bioController.text.trim().isEmpty ? 'null' : _bioController.text.trim()}');
      print('EditProfileDialog: Mobile: ${_mobileController.text.trim().isEmpty ? 'null' : _mobileController.text.trim()}');
      print('EditProfileDialog: Address: ${_addressController.text.trim().isEmpty ? 'null' : _addressController.text.trim()}');
      print('EditProfileDialog: Country: ${_countryController.text.trim().isEmpty ? 'null' : _countryController.text.trim()}');
      print('EditProfileDialog: Gender: $_selectedGender');

      await UserManagementService.updateUserProfile(
        widget.user.id,
        displayName: _displayNameController.text.trim(),
        username: _usernameController.text.trim(),
        bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
        mobile: _mobileController.text.trim().isEmpty ? null : _mobileController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        country: _countryController.text.trim().isEmpty ? null : _countryController.text.trim(),
        gender: _selectedGender,
      );

      print('EditProfileDialog: Profile update successful');
      if (mounted) {
        print('EditProfileDialog: Closing dialog and triggering callback');
        Navigator.of(context).pop();
        widget.onProfileUpdated();
      }
    } catch (e) {
      print('EditProfileDialog: Error occurred during profile update: $e');
      print('EditProfileDialog: Error type: ${e.runtimeType}');
      print('EditProfileDialog: Stack trace: ${StackTrace.current}');

      if (mounted) {
        print('EditProfileDialog: Showing error snackbar to user');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $e'),
            backgroundColor: AppConstants.errorColor,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      print('EditProfileDialog: Cleaning up - setting loading to false');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 600,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9 > 600
              ? 600
              : MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                        backgroundImage: widget.user.profileImageUrl != null
                            ? NetworkImage(widget.user.profileImageUrl!)
                            : null,
                        child: widget.user.profileImageUrl == null
                            ? Text(
                                widget.user.displayName.isNotEmpty
                                    ? widget.user.displayName[0].toUpperCase()
                                    : widget.user.email[0].toUpperCase(),
                                style: const TextStyle(
                                  color: AppConstants.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Edit User Profile',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              widget.user.email,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Personal Information Section
                  _buildSectionHeader('Personal Information', Icons.person),
                  const SizedBox(height: AppConstants.paddingMedium),

                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _displayNameController,
                          label: 'Display Name',
                          icon: Icons.person_outline,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Display name is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: _buildTextField(
                          controller: _usernameController,
                          label: 'Username',
                          icon: Icons.alternate_email,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Username is required';
                            }
                            if (value.trim().length < 3) {
                              return 'Username must be at least 3 characters';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  _buildTextField(
                    controller: _bioController,
                    label: 'Bio (Optional)',
                    icon: Icons.description_outlined,
                    maxLines: 3,
                    maxLength: 500,
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Contact Information Section
                  _buildSectionHeader('Contact Information', Icons.contact_phone),
                  const SizedBox(height: AppConstants.paddingMedium),

                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _mobileController,
                          label: 'Mobile (Optional)',
                          icon: Icons.phone_outlined,
                          keyboardType: TextInputType.phone,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: _buildGenderDropdown(),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  _buildTextField(
                    controller: _addressController,
                    label: 'Address (Optional)',
                    icon: Icons.location_on_outlined,
                    maxLines: 2,
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  _buildTextField(
                    controller: _countryController,
                    label: 'Country (Optional)',
                    icon: Icons.public_outlined,
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Action Buttons
                  Container(
                    padding: const EdgeInsets.only(top: AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                          label: const Text('Cancel'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        ElevatedButton.icon(
                          onPressed: _isLoading ? null : _updateProfile,
                          icon: _isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.save),
                          label: const Text('Update Profile'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConstants.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppConstants.paddingLarge,
                              vertical: AppConstants.paddingMedium,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppConstants.primaryColor, size: 20),
        const SizedBox(width: AppConstants.paddingSmall),
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
    int? maxLength,
    TextInputType? keyboardType,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        counterText: maxLength != null ? null : '',
      ),
      validator: validator,
      maxLines: maxLines,
      maxLength: maxLength,
      keyboardType: keyboardType,
    );
  }

  Widget _buildGenderDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGender,
      decoration: const InputDecoration(
        labelText: 'Gender (Optional)',
        prefixIcon: Icon(Icons.person_outline),
        border: OutlineInputBorder(),
      ),
      items: const [
        DropdownMenuItem(value: null, child: Text('Not specified')),
        DropdownMenuItem(value: 'Male', child: Text('Male')),
        DropdownMenuItem(value: 'Female', child: Text('Female')),
        DropdownMenuItem(value: 'Other', child: Text('Other')),
      ],
      onChanged: (value) {
        setState(() {
          _selectedGender = value;
        });
      },
    );
  }
}
