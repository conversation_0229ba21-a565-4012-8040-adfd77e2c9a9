import 'package:cloud_firestore/cloud_firestore.dart';

enum NotificationType {
  broadcast, // Send to all users
  targeted, // Send to specific users
  role, // Send to users with specific roles
}

enum NotificationPriority {
  low,
  normal,
  high,
}

class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final Map<String, dynamic>? data;
  final NotificationType type;
  final NotificationPriority priority;
  final List<String> targetUserIds; // For targeted notifications
  final List<String> targetRoles; // For role-based notifications
  final String createdBy;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final bool isSent;
  final int totalRecipients;
  final int successfulSends;
  final int failedSends;
  final Map<String, dynamic>? metadata;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    this.data,
    required this.type,
    this.priority = NotificationPriority.normal,
    this.targetUserIds = const [],
    this.targetRoles = const [],
    required this.createdBy,
    required this.createdAt,
    this.scheduledAt,
    this.isSent = false,
    this.totalRecipients = 0,
    this.successfulSends = 0,
    this.failedSends = 0,
    this.metadata,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      imageUrl: data['imageUrl'],
      data: data['data'] as Map<String, dynamic>?,
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == 'NotificationType.${data['type']}',
        orElse: () => NotificationType.broadcast,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString() == 'NotificationPriority.${data['priority']}',
        orElse: () => NotificationPriority.normal,
      ),
      targetUserIds: List<String>.from(data['targetUserIds'] ?? []),
      targetRoles: List<String>.from(data['targetRoles'] ?? []),
      createdBy: data['createdBy'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      scheduledAt: (data['scheduledAt'] as Timestamp?)?.toDate(),
      isSent: data['isSent'] ?? false,
      totalRecipients: data['totalRecipients'] ?? 0,
      successfulSends: data['successfulSends'] ?? 0,
      failedSends: data['failedSends'] ?? 0,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'data': data,
      'type': type.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'targetUserIds': targetUserIds,
      'targetRoles': targetRoles,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'scheduledAt': scheduledAt != null ? Timestamp.fromDate(scheduledAt!) : null,
      'isSent': isSent,
      'totalRecipients': totalRecipients,
      'successfulSends': successfulSends,
      'failedSends': failedSends,
      'metadata': metadata,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? imageUrl,
    Map<String, dynamic>? data,
    NotificationType? type,
    NotificationPriority? priority,
    List<String>? targetUserIds,
    List<String>? targetRoles,
    String? createdBy,
    DateTime? createdAt,
    DateTime? scheduledAt,
    bool? isSent,
    int? totalRecipients,
    int? successfulSends,
    int? failedSends,
    Map<String, dynamic>? metadata,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      targetUserIds: targetUserIds ?? this.targetUserIds,
      targetRoles: targetRoles ?? this.targetRoles,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      isSent: isSent ?? this.isSent,
      totalRecipients: totalRecipients ?? this.totalRecipients,
      successfulSends: successfulSends ?? this.successfulSends,
      failedSends: failedSends ?? this.failedSends,
      metadata: metadata ?? this.metadata,
    );
  }

  String get priorityDisplayName {
    switch (priority) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case NotificationType.broadcast:
        return 'Broadcast to All';
      case NotificationType.targeted:
        return 'Targeted Users';
      case NotificationType.role:
        return 'Role-based';
    }
  }
}
