# FCM Usage Examples

## Complete Implementation Example

### 1. Sending Broadcast Notification (All Users)

```dart
// From the admin app
import 'package:admin/services/notification_service.dart';
import 'package:admin/models/notification_model.dart';

Future<void> sendBroadcastNotification() async {
  final notificationService = NotificationService();
  
  final notification = NotificationModel(
    id: '',
    title: 'New Feature Available!',
    body: 'Check out our latest update with exciting new features.',
    imageUrl: 'https://example.com/feature-image.jpg',
    type: NotificationType.broadcast,
    priority: NotificationPriority.high,
    createdBy: 'admin_user_id',
    createdAt: DateTime.now(),
  );
  
  final result = await notificationService.sendNotification(notification);
  
  if (result['success']) {
    print('Notification sent to ${result['totalRecipients']} users');
    print('Successful sends: ${result['successfulSends']}');
    print('Failed sends: ${result['failedSends']}');
  } else {
    print('Failed to send notification: ${result['error']}');
  }
}
```

### 2. Sending Targeted Notification (Specific Users)

```dart
Future<void> sendTargetedNotification(List<String> userIds) async {
  final notificationService = NotificationService();
  
  final notification = NotificationModel(
    id: '',
    title: 'Personal Message',
    body: 'You have been selected for a special offer!',
    type: NotificationType.targeted,
    priority: NotificationPriority.normal,
    targetUserIds: userIds,
    createdBy: 'admin_user_id',
    createdAt: DateTime.now(),
    data: {
      'action': 'open_offers',
      'offer_id': 'special_2024',
    },
  );
  
  final result = await notificationService.sendNotification(notification);
  print('Targeted notification result: $result');
}
```

### 3. Sending Role-Based Notification

```dart
Future<void> sendResellerNotification() async {
  final notificationService = NotificationService();
  
  final notification = NotificationModel(
    id: '',
    title: 'Reseller Update',
    body: 'New commission rates are now available. Check your dashboard.',
    type: NotificationType.role,
    priority: NotificationPriority.high,
    targetRoles: ['reseller'],
    createdBy: 'admin_user_id',
    createdAt: DateTime.now(),
    data: {
      'action': 'open_dashboard',
      'section': 'commissions',
    },
  );
  
  final result = await notificationService.sendNotification(notification);
  print('Reseller notification result: $result');
}
```

### 4. Testing Individual Token

```dart
Future<void> testNotificationToToken(String fcmToken) async {
  final notificationService = NotificationService();
  
  final result = await notificationService.testNotification(fcmToken);
  
  if (result['success']) {
    print('Test notification sent successfully');
  } else {
    print('Test notification failed: ${result['error']}');
  }
}
```

### 5. Getting Notification Statistics

```dart
Future<void> displayNotificationStats() async {
  final notificationService = NotificationService();
  
  final stats = await notificationService.getNotificationStats();
  
  print('Notifications sent today: ${stats['today']}');
  print('Notifications sent this week: ${stats['week']}');
  print('Notifications sent this month: ${stats['month']}');
  print('Total notifications: ${stats['total']}');
  print('Active FCM tokens: ${stats['activeTokens']}');
}
```

### 6. Handling FCM in User App

```dart
// In your user app's main.dart
import 'package:firebase_messaging/firebase_messaging.dart';

// Background message handler (top-level function)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('Handling a background message: ${message.messageId}');
  
  // Handle the message data
  if (message.data.isNotEmpty) {
    // Navigate to specific screen or perform action
    handleNotificationData(message.data);
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Set the background messaging handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    setupFCM();
  }
  
  void setupFCM() async {
    // Request permission
    NotificationSettings settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
      
      // Get token
      String? token = await FirebaseMessaging.instance.getToken();
      print('FCM Token: $token');
      
      // Store token in Firestore (associate with user)
      if (token != null) {
        await storeTokenInFirestore(token);
      }
      
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');
        
        if (message.notification != null) {
          // Show in-app notification
          showInAppNotification(message.notification!);
        }
      });
      
      // Handle notification taps
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('A new onMessageOpenedApp event was published!');
        handleNotificationTap(message);
      });
      
      // Handle app opened from terminated state
      FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
        if (message != null) {
          handleNotificationTap(message);
        }
      });
    }
  }
  
  Future<void> storeTokenInFirestore(String token) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      await FirebaseFirestore.instance.collection('fcm_tokens').add({
        'userId': user.uid,
        'token': token,
        'platform': Platform.isAndroid ? 'android' : 'ios',
        'deviceId': 'device_${DateTime.now().millisecondsSinceEpoch}',
        'createdAt': FieldValue.serverTimestamp(),
        'lastUsed': FieldValue.serverTimestamp(),
        'isActive': true,
      });
    }
  }
  
  void showInAppNotification(RemoteNotification notification) {
    // Show a snackbar or dialog for foreground notifications
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.title ?? '', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(notification.body ?? ''),
          ],
        ),
        duration: Duration(seconds: 4),
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            // Handle notification tap
          },
        ),
      ),
    );
  }
  
  void handleNotificationTap(RemoteMessage message) {
    // Navigate based on message data
    final data = message.data;
    
    if (data['action'] == 'open_offers') {
      // Navigate to offers screen
      Navigator.pushNamed(context, '/offers');
    } else if (data['action'] == 'open_dashboard') {
      // Navigate to dashboard
      Navigator.pushNamed(context, '/dashboard');
    }
    // Add more navigation logic as needed
  }
  
  void handleNotificationData(Map<String, dynamic> data) {
    // Handle background notification data
    print('Background notification data: $data');
    
    // You can store data locally or trigger app state changes
    // This runs even when the app is closed
  }
  
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Your App',
      home: HomeScreen(),
      routes: {
        '/offers': (context) => OffersScreen(),
        '/dashboard': (context) => DashboardScreen(),
      },
    );
  }
}
```

### 7. Cloud Function Direct Call Example

```javascript
// If you want to call the function directly (for testing)
const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Test function call
const testSendNotification = async () => {
  const data = {
    title: 'Test Notification',
    body: 'This is a test notification from Cloud Function',
    type: 'broadcast',
    priority: 'high'
  };
  
  const context = {
    auth: {
      uid: 'admin_user_id' // Replace with actual admin user ID
    }
  };
  
  try {
    const result = await sendNotification(data, context);
    console.log('Notification sent:', result);
  } catch (error) {
    console.error('Error sending notification:', error);
  }
};
```

### 8. Scheduled Notifications Example

```dart
// You can extend the system to support scheduled notifications
Future<void> scheduleNotification(DateTime scheduledTime) async {
  final notification = NotificationModel(
    id: '',
    title: 'Scheduled Reminder',
    body: 'This is your scheduled reminder!',
    type: NotificationType.broadcast,
    priority: NotificationPriority.normal,
    createdBy: 'admin_user_id',
    createdAt: DateTime.now(),
    scheduledAt: scheduledTime, // Add this field to your model
  );
  
  // Store in Firestore with scheduled time
  await FirebaseFirestore.instance.collection('scheduled_notifications').add(
    notification.toFirestore()
  );
  
  // You would need a Cloud Function with a scheduler to process these
}
```

## Best Practices

1. **Always validate input data**
2. **Handle errors gracefully**
3. **Implement proper rate limiting**
4. **Clean up invalid tokens regularly**
5. **Use appropriate notification priorities**
6. **Test thoroughly on different devices**
7. **Monitor notification delivery rates**
8. **Respect user preferences for notifications**

## Testing Checklist

- [ ] FCM token generation works
- [ ] Notifications received in foreground
- [ ] Notifications received in background
- [ ] Notifications received when app is closed
- [ ] Notification taps navigate correctly
- [ ] Admin panel sends notifications successfully
- [ ] Invalid tokens are cleaned up
- [ ] Rate limiting works
- [ ] Different notification types work (broadcast, targeted, role-based)
- [ ] Statistics are accurate
